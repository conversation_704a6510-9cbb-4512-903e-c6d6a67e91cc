<div class="hidden md:flex md:flex-shrink-0">
    <div class="flex flex-col w-64 shadow-xl">
        <!-- Sidebar component, show/hide based on sidebar state. -->
        <div class="flex flex-col h-0 flex-1 bg-[#1E40AF] dark:bg-gray-900 relative overflow-hidden transition-colors duration-200">
            <!-- Decorative elements -->
            <div class="absolute -right-10 -top-10 w-40 h-40 bg-white/10 dark:bg-blue-900/30 rounded-full"></div>
            <div class="absolute -right-20 top-1/3 w-60 h-60 bg-white/5 dark:bg-blue-900/20 rounded-full"></div>
            <div class="absolute -left-10 bottom-0 w-32 h-32 bg-white/5 dark:bg-blue-900/20 rounded-full"></div>
            
            <div class="flex-1 flex flex-col pt-8 pb-4 overflow-y-auto relative z-10">
                <div class="flex items-center flex-shrink-0 px-6 py-6 border-b border-white/10">
                    <div class="bg-white/20 p-2 rounded-lg mr-3 shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-white text-xl font-bold">FBNE</h1>
                        <!-- <p class="text-xs text-white/60">Dashboard</p> -->
                    </div>
                </div>
                <nav class="mt-2 flex-1 px-4 space-y-1 overflow-y-auto">
                    <!-- Theme Toggle for Mobile -->
                    <div class="md:hidden mb-4 p-2 bg-white/10 dark:bg-gray-700/30 rounded-lg">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-white/90">Dark Mode</span>
                            <button @click="$store.theme.toggle()" 
                                    type="button"
                                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none bg-white/20"
                                    role="switch"
                                    :aria-checked="$store.theme.dark">
                                <span class="sr-only">Toggle dark mode</span>
                                <span aria-hidden="true" 
                                      :class="{'translate-x-5': $store.theme.dark, 'translate-x-0': !$store.theme.dark}"
                                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"></span>
                            </button>
                        </div>
                    </div>
                    @php
                        $currentPath = request()->path();
                        $isAdmin = Auth::check() && strtolower(Auth::user()->role) === 'admin';
                        $navItems = [
                            'dashboard' => [
                                'url' => $isAdmin ? '/admin/dashboard' : '/secretary/dashboard',
                                'icon' => 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
                                'text' => 'Dashboard'
                            ],


                            'documents' => [
                                'url' => '/documents',
                                'icon' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                                'text' => 'Document Tracker'
                            ],
                            'promotions' => $isAdmin ? [
                                'url' => '/promotions',
                                'icon' => 'M3 3h18v18H3V3z',
                                'text' => 'Promotions'
                             ] : null,
                            'students' => $isAdmin ? [
                                'url' => '/students',
                                'icon' => 'M12 14l9-5-9-5-9 5 9 5z',
                                'text' => 'Students'
                             ] : null,

                            'users' => $isAdmin ? [
                                'url' => '/users',
                                'icon' => 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
                                'text' => 'User Management'
                             ] : null,
                        
                         
                            'activity_log' => [
                                'url' => '/activity_log',
                                'icon' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                                'text' => 'Activity Log'
                            ],
                         
                        ];
                    @endphp

                    @foreach(array_filter($navItems) as $key => $item)
                        @php
                            $isActive = request()->is(ltrim($item['url'], '/') . '*');
                        @endphp
                        @if(isset($item['children']))
                            <div x-data="{ open: {{ $isActive ? 'true' : 'false' }} }" class="relative">
                                <button @click="open = !open" class="w-full group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 mb-1 {{ $isActive ? 'bg-white/20 text-white shadow-lg' : 'text-white/80 hover:bg-white/10 hover:text-white' }}">
                                    <div class="p-1.5 mr-3 rounded-lg {{ $isActive ? 'bg-white/30' : 'bg-white/10 group-hover:bg-white/20' }}">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="{{ $isActive ? '2' : '1.5' }}">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="{{ $item['icon'] }}" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">{{ $item['text'] }}</span>
                                    <svg class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                <div x-show="open" x-collapse class="ml-8 pl-2 border-l-2 border-white/10">
                                    @foreach($item['children'] as $childKey => $child)
                                        @php
                                            $isChildActive = request()->is(ltrim($child['url'], '/') . '*');
                                        @endphp
                                        <a href="{{ $child['url'] }}" class="group flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 mb-1 {{ $isChildActive ? 'bg-white/20 text-white' : 'text-white/60 hover:bg-white/10 hover:text-white' }}">
                                            <div class="p-1 mr-3 rounded-lg {{ $isChildActive ? 'bg-white/30' : 'bg-white/10 group-hover:bg-white/20' }}">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="{{ $isChildActive ? '2' : '1.5' }}">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="{{ $child['icon'] }}" />
                                                </svg>
                                            </div>
                                            <span>{{ $child['text'] }}</span>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <a href="{{ $item['url'] }}" 
                               class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 mb-1
                                      {{ $isActive ? 'bg-white/20 text-white shadow-lg' : 'text-white/80 hover:bg-white/10 hover:text-white' }}">
                                <div class="p-1.5 mr-3 rounded-lg {{ $isActive ? 'bg-white/30' : 'bg-white/10 group-hover:bg-white/20' }}">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="{{ $isActive ? '2' : '1.5' }}">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="{{ $item['icon'] }}" />
                                    </svg>
                                </div>
                                <span class="font-medium">{{ $item['text'] }}</span>
                                @if($isActive)
                                    <span class="ml-auto h-2 w-2 rounded-full bg-blue-300"></span>
                                @endif
                            </a>
                        @endif
                    @endforeach
                </nav>
            </div>
            <div class="flex-shrink-0 flex border-t border-white/10 p-4 mt-auto">
                <div class="flex items-center w-full bg-white/10 hover:bg-white/20 transition-colors rounded-xl p-3 cursor-pointer group">
                    <div class="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-bold text-lg shadow-md">
                        {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                    </div>
                    <div class="ml-3 overflow-hidden">
                        <div class="text-sm font-semibold text-white group-hover:text-blue-100 transition-colors">
                            {{ Auth::user()->name }}
                        </div>
                        <div class="text-xs text-white/70 group-hover:text-white/80 transition-colors">
                            {{ Auth::user()->email }}
                        </div>
                    </div>
                    <div class="ml-auto text-white/60 group-hover:text-white/80">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile menu button -->
<div class="md:hidden fixed top-4 left-4 z-50">
    <button type="button" @click="sidebarOpen = !sidebarOpen" class="inline-flex items-center justify-center p-2.5 rounded-xl bg-[#3572EF] text-white shadow-lg hover:bg-[#2b62d3] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#3572EF] focus:ring-white">
        <span class="sr-only">Open main menu</span>
        <svg x-show="!sidebarOpen" class="block h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        <svg x-show="sidebarOpen" class="hidden h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12" />
        </svg>
    </button>
</div>

<!-- Mobile sidebar overlay -->
<div x-show="sidebarOpen" 
     @click.self="sidebarOpen = false"
     x-transition:enter="transition-opacity ease-linear duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition-opacity ease-linear duration-300"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 z-40 bg-black/50 md:hidden"></div>

<!-- Mobile sidebar -->
<div x-show="sidebarOpen" 
     x-transition:enter="transition ease-in-out duration-300 transform"
     x-transition:enter-start="-translate-x-full"
     x-transition:enter-end="translate-x-0"
     x-transition:leave="transition ease-in-out duration-300 transform"
     x-transition:leave-start="translate-x-0"
     x-transition:leave-end="-translate-x-full"
     class="fixed inset-y-0 left-0 z-50 w-72 bg-[#3572EF] dark:bg-gray-800 shadow-xl transition-colors duration-200">
    <div class="h-full overflow-y-auto">
        <div class="flex items-center justify-between p-6">
            <div class="flex items-center">
                <div class="bg-white/10 p-2 rounded-lg mr-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                </div>
                <h1 class="text-white text-2xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">FBNE</h1>
            </div>
            <button @click="sidebarOpen = false" class="text-white/70 hover:text-white focus:outline-none">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="px-4 py-4 space-y-1">
        @foreach(array_filter($navItems) as $key => $item)
            @if(isset($item['children']))
                <div x-data="{ open: {{ request()->is(ltrim($item['url'], '/') . '*') ? 'true' : 'false' }} }" class="relative">
                    <button @click="open = !open" class="w-full group flex items-center px-4 py-3 text-base font-medium rounded-xl transition-all duration-200 mb-1 {{ request()->is(ltrim($item['url'], '/') . '*') ? 'bg-white/20 text-white shadow-lg' : 'text-white/90 hover:bg-white/10 hover:text-white' }}">
                        <div class="p-1.5 mr-4 rounded-lg {{ request()->is(ltrim($item['url'], '/') . '*') ? 'bg-white/20' : 'bg-white/5 group-hover:bg-white/10' }}">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $item['icon'] }}" />
                            </svg>
                        </div>
                        <span class="font-medium">{{ $item['text'] }}</span>
                        <svg class="ml-auto h-5 w-5 transform transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="open" x-collapse class="ml-8 pl-2 border-l-2 border-white/10">
                        @foreach($item['children'] as $childKey => $child)
                            @php
                                $isChildActive = request()->is(ltrim($child['url'], '/') . '*');
                            @endphp
                            <a href="{{ $child['url'] }}" class="group flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 mb-1 {{ $isChildActive ? 'bg-white/20 text-white' : 'text-white/60 hover:bg-white/10 hover:text-white' }}">
                                <div class="p-1 mr-3 rounded-lg {{ $isChildActive ? 'bg-white/30' : 'bg-white/10 group-hover:bg-white/20' }}">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="{{ $isChildActive ? '2' : '1.5' }}">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="{{ $child['icon'] }}" />
                                    </svg>
                                </div>
                                <span>{{ $child['text'] }}</span>
                            </a>
                        @endforeach
                    </div>
                </div>
            @else
                <a href="{{ $item['url'] }}" 
                   class="group flex items-center px-4 py-3 text-base font-medium rounded-xl transition-all duration-200
                          {{ request()->is(ltrim($item['url'], '/') . '*') ? 'bg-white/20 text-white shadow-lg' : 'text-white/90 hover:bg-white/10 hover:text-white' }}">
                    <div class="p-1.5 mr-4 rounded-lg {{ request()->is(ltrim($item['url'], '/') . '*') ? 'bg-white/20' : 'bg-white/5 group-hover:bg-white/10' }}">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $item['icon'] }}" />
                        </svg>
                    </div>
                    <span class="font-medium">{{ $item['text'] }}</span>
                    @if(request()->is(ltrim($item['url'], '/') . '*'))
                        <span class="ml-auto h-2 w-2 rounded-full bg-white/80"></span>
                    @endif
                </a>
            @endif
        @endforeach
    </div>
    <div class="pt-4 pb-3 border-t border-gray-700">
        <div class="flex items-center px-5">
            <div class="ml-3">
                <div class="text-base font-medium text-white">{{ Auth::user()->name }}</div>
                <div class="text-sm font-medium text-gray-400">{{ Auth::user()->email }}</div>
            </div>
        </div>
    </div>
</div>
