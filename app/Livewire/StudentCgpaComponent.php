<?php

namespace App\Livewire;

use App\Models\StudentCgpa;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StudentCgpaTemplateExport;
use App\Imports\StudentCgpaImport;

class StudentCgpaComponent extends Component
{
    use WithPagination, WithFileUploads;

    // Search and filter properties
    public $search = '';
    public $programFilter = '';
    public $departmentFilter = '';
    public $semesterFilter = '';

    // Modal properties
    public $showModal = false;
    public $showImportModal = false;
    public $editMode = false;
    public $studentId = null;

    // Form properties
    public $index_number = '';
    public $name = '';
    public $gpa = '';
    public $cgpa = '';
    public $totalpoint = '';
    public $totalcredit = '';
    public $program = '';
    public $department = '';
    public $semester = '';
    public $year_of_enrollment = '';
    public $email = '';

    // Import properties
    public $importFile = null;

    // Confirmation modal
    public $confirmingDeletion = false;
    public $studentToDelete = null;

    protected $rules = [
        'index_number' => 'required|string|max:255',
        'name' => 'required|string|max:255',
        'gpa' => 'required|numeric|min:0|max:4',
        'cgpa' => 'required|numeric|min:0|max:4',
        'totalpoint' => 'required|numeric|min:0',
        'totalcredit' => 'required|numeric|min:0',
        'program' => 'required|string|max:255',
        'department' => 'required|string|max:255',
        'semester' => 'required|string|max:255',
        'year_of_enrollment' => 'required|integer|min:1900|max:' . (date('Y') + 10),
        'email' => 'nullable|email|max:255',
    ];

    protected $messages = [
        'index_number.required' => 'Index number is required.',
        'name.required' => 'Student name is required.',
        'gpa.required' => 'GPA is required.',
        'gpa.numeric' => 'GPA must be a number.',
        'gpa.min' => 'GPA must be at least 0.',
        'gpa.max' => 'GPA cannot exceed 4.',
        'cgpa.required' => 'CGPA is required.',
        'cgpa.numeric' => 'CGPA must be a number.',
        'cgpa.min' => 'CGPA must be at least 0.',
        'cgpa.max' => 'CGPA cannot exceed 4.',
        'totalpoint.required' => 'Total point is required.',
        'totalcredit.required' => 'Total credit is required.',
        'program.required' => 'Program is required.',
        'department.required' => 'Department is required.',
        'semester.required' => 'Semester is required.',
        'year_of_enrollment.required' => 'Year of enrollment is required.',
        'email.email' => 'Please enter a valid email address.',
    ];

    public function mount()
    {
        $this->year_of_enrollment = date('Y');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingProgramFilter()
    {
        $this->resetPage();
    }

    public function updatingDepartmentFilter()
    {
        $this->resetPage();
    }

    public function updatingSemesterFilter()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->programFilter = '';
        $this->departmentFilter = '';
        $this->semesterFilter = '';
        $this->resetPage();
    }

    public function openModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->showModal = true;
    }

    public function editStudent($id)
    {
        $student = StudentCgpa::findOrFail($id);
        
        $this->studentId = $student->id;
        $this->index_number = $student->index_number;
        $this->name = $student->name;
        $this->gpa = $student->gpa;
        $this->cgpa = $student->cgpa;
        $this->totalpoint = $student->totalpoint;
        $this->totalcredit = $student->totalcredit;
        $this->program = $student->program;
        $this->department = $student->department;
        $this->semester = $student->semester;
        $this->year_of_enrollment = $student->year_of_enrollment;
        $this->email = $student->email;
        
        $this->editMode = true;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function closeImportModal()
    {
        $this->showImportModal = false;
        $this->importFile = null;
    }

    public function resetForm()
    {
        $this->studentId = null;
        $this->index_number = '';
        $this->name = '';
        $this->gpa = '';
        $this->cgpa = '';
        $this->totalpoint = '';
        $this->totalcredit = '';
        $this->program = '';
        $this->department = '';
        $this->semester = '';
        $this->year_of_enrollment = date('Y');
        $this->email = '';
        $this->resetErrorBag();
    }

    public function save()
    {
        $this->validate();

        try {
            $data = [
                'index_number' => $this->index_number,
                'name' => $this->name,
                'gpa' => $this->gpa,
                'cgpa' => $this->cgpa,
                'totalpoint' => $this->totalpoint,
                'totalcredit' => $this->totalcredit,
                'program' => $this->program,
                'department' => $this->department,
                'semester' => $this->semester,
                'year_of_enrollment' => $this->year_of_enrollment,
                'email' => $this->email,
            ];

            if ($this->editMode) {
                $student = StudentCgpa::findOrFail($this->studentId);
                $student->update($data);
                flash()->success('Student updated successfully!');
            } else {
                StudentCgpa::create($data);
                flash()->success('Student created successfully!');
            }

            $this->closeModal();
        } catch (\Exception $e) {
            flash()->error('An error occurred while saving the student.');
        }
    }

    public function confirmDelete($id)
    {
        $this->studentToDelete = $id;
        $this->confirmingDeletion = true;
    }

    public function cancelDelete()
    {
        $this->confirmingDeletion = false;
        $this->studentToDelete = null;
    }

    public function deleteStudent()
    {
        try {
            if ($this->studentToDelete) {
                StudentCgpa::findOrFail($this->studentToDelete)->delete();
                flash()->success('Student deleted successfully!');
            }
        } catch (\Exception $e) {
            flash()->error('An error occurred while deleting the student.');
        } finally {
            $this->cancelDelete();
        }
    }

    public function openImportModal()
    {
        $this->showImportModal = true;
    }

    public function downloadTemplate()
    {
        return Excel::download(new StudentCgpaTemplateExport, 'student_cgpa_template.xlsx');
    }

    public function importStudents()
    {
        $this->validate([
            'importFile' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);

        try {
            Excel::import(new StudentCgpaImport, $this->importFile);
            flash()->success('Students imported successfully!');
            $this->closeImportModal();
        } catch (\Exception $e) {
            flash()->error('Error importing students: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $students = StudentCgpa::search($this->search)
            ->byProgram($this->programFilter)
            ->byDepartment($this->departmentFilter)
            ->bySemester($this->semesterFilter)
            ->latest()
            ->paginate(15);

        $programs = StudentCgpa::getUniquePrograms();
        $departments = StudentCgpa::getUniqueDepartments();
        $semesters = StudentCgpa::getUniqueSemesters();

        return view('livewire.student-cgpa-component', compact('students', 'programs', 'departments', 'semesters'));
    }
}
