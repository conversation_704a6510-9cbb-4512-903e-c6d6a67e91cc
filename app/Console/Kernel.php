<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     */
    protected $commands = [
        \App\Console\Commands\SendPromotionExpiryAlerts::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run daily at 8am server time
        $schedule->command('promotions:alert')->dailyAt('08:00');
        
        // Send poor performance notifications twice a year (January and July)
        $schedule->job(new \App\Jobs\SendPoorPerformanceNotifications())
            ->cron('0 9 15 1,7 *') // 9:00 AM on the 15th of January and July
            ->timezone('Africa/Accra')
            ->emailOutputOnFailure(env('ADMIN_EMAIL'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
